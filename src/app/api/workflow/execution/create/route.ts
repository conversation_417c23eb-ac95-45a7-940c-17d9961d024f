/**
 * API endpoint to create workflow executions in the same Redis system
 * as the working executions (ceef6bd0-a842-47bb-aade-0d36efa08c90, etc.)
 */

import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      executionId, 
      name, 
      description, 
      status, 
      startedAt, 
      steps, 
      metadata 
    } = body;

    console.log(`🔄 Creating workflow execution: ${executionId}`);

    // Validate required fields
    if (!executionId || !name || !status) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: executionId, name, status'
      }, { status: 400 });
    }

    // Import the workflow engine to access the same Redis storage
    const { WorkflowEngine } = await import('../../../../../core/workflow/WorkflowEngine');
    const workflowEngine = WorkflowEngine.getInstance();

    // Create execution data in the same format as working executions
    const executionData = {
      id: executionId,
      workflowId: `workflow-${executionId}`,
      name,
      description: description || 'Agent-Enhanced Workflow',
      status,
      startedAt,
      completedAt: status === 'completed' ? new Date().toISOString() : undefined,
      progress: status === 'completed' ? 100 : 0,
      steps: steps || [],
      metadata: {
        ...metadata,
        createdBy: 'agent-enhanced-system',
        createdAt: new Date().toISOString()
      },
      artifacts: [],
      logs: []
    };

    // Store in the workflow engine's storage system
    await workflowEngine.storeExecution(executionId, executionData);

    console.log(`✅ Workflow execution created: ${executionId}`);

    return NextResponse.json({
      success: true,
      data: {
        executionId,
        status: 'created',
        message: 'Workflow execution created successfully'
      }
    });

  } catch (error) {
    console.error('❌ Failed to create workflow execution:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create workflow execution'
    }, { status: 500 });
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json({
    success: false,
    error: 'Method not allowed. Use POST to create executions.'
  }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({
    success: false,
    error: 'Method not allowed. Use POST to create executions.'
  }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({
    success: false,
    error: 'Method not allowed. Use POST to create executions.'
  }, { status: 405 });
}
