/**
 * AI Generation API
 * 
 * Provides AI content generation for workflow steps
 * This endpoint was missing and causing 404 errors in workflow execution
 */

import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

interface AIGenerationRequest {
  prompt: string;
  context?: {
    topic?: string;
    targetAudience?: string;
    contentType?: string;
    keywords?: string[];
    tone?: string;
    stepId?: string;
    workflowExecutionId?: string;
  };
  options?: {
    maxTokens?: number;
    temperature?: number;
    model?: string;
  };
}

interface AIGenerationResponse {
  success: boolean;
  data?: {
    content: string;
    metadata: {
      model: string;
      tokensUsed: number;
      processingTime: number;
      stepId?: string;
      workflowExecutionId?: string;
    };
  };
  error?: string;
}

/**
 * POST /api/ai/generate
 * Generate AI content for workflow steps
 */
export async function POST(request: NextRequest): Promise<NextResponse<AIGenerationResponse>> {
  const startTime = Date.now();
  
  try {
    const body: AIGenerationRequest = await request.json();
    const { prompt, context = {}, options = {} } = body;

    // Validate required fields
    if (!prompt) {
      return NextResponse.json({
        success: false,
        error: 'Prompt is required'
      }, { status: 400 });
    }

    // Validate OpenAI API key
    if (!process.env.OPENAI_API_KEY) {
      console.error('❌ OpenAI API key not configured');
      return NextResponse.json({
        success: false,
        error: 'AI service not configured'
      }, { status: 500 });
    }

    console.log(`🤖 Generating AI content for step: ${context.stepId || 'unknown'}`);

    // Build enhanced prompt with context
    const enhancedPrompt = buildEnhancedPrompt(prompt, context);

    // Set default options
    const modelOptions = {
      model: options.model || 'gpt-3.5-turbo',
      temperature: options.temperature || 0.7,
      max_tokens: options.maxTokens || 1000,
    };

    // Generate content using OpenAI
    const completion = await openai.chat.completions.create({
      ...modelOptions,
      messages: [
        {
          role: 'system',
          content: 'You are a professional content creator that generates high-quality, engaging content for various purposes.'
        },
        {
          role: 'user',
          content: enhancedPrompt
        }
      ],
    });

    const generatedContent = completion.choices[0]?.message?.content;
    
    if (!generatedContent) {
      throw new Error('No content generated from AI service');
    }

    const processingTime = Date.now() - startTime;

    console.log(`✅ AI content generated successfully in ${processingTime}ms`);

    return NextResponse.json({
      success: true,
      data: {
        content: generatedContent,
        metadata: {
          model: modelOptions.model,
          tokensUsed: completion.usage?.total_tokens || 0,
          processingTime,
          stepId: context.stepId,
          workflowExecutionId: context.workflowExecutionId,
        }
      }
    });

  } catch (error) {
    const processingTime = Date.now() - startTime;
    console.error('❌ AI generation failed:', error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'AI generation failed'
    }, { status: 500 });
  }
}

/**
 * Build enhanced prompt with context information
 */
function buildEnhancedPrompt(basePrompt: string, context: AIGenerationRequest['context']): string {
  let enhancedPrompt = basePrompt;

  if (context?.topic) {
    enhancedPrompt += `\n\nTopic: ${context.topic}`;
  }

  if (context?.targetAudience) {
    enhancedPrompt += `\nTarget Audience: ${context.targetAudience}`;
  }

  if (context?.contentType) {
    enhancedPrompt += `\nContent Type: ${context.contentType}`;
  }

  if (context?.keywords && context.keywords.length > 0) {
    enhancedPrompt += `\nKeywords to include: ${context.keywords.join(', ')}`;
  }

  if (context?.tone) {
    enhancedPrompt += `\nTone: ${context.tone}`;
  }

  return enhancedPrompt;
}

/**
 * GET /api/ai/generate
 * Get AI generation service status
 */
export async function GET(): Promise<NextResponse> {
  return NextResponse.json({
    success: true,
    data: {
      status: 'active',
      service: 'AI Content Generation',
      models: ['gpt-3.5-turbo', 'gpt-4'],
      capabilities: [
        'content-generation',
        'text-completion',
        'context-aware-generation'
      ],
      version: '1.0.0'
    }
  });
}
