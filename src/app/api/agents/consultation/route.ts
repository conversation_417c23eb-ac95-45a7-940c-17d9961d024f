/**
 * Agent Consultation API
 * 
 * Provides endpoints for managing and monitoring the dynamic agent consultation system
 */

import { NextRequest, NextResponse } from 'next/server';
import { DynamicAgentConsultationService } from '../../../../core/workflow/dynamic-agent-consultation-service';
import { WorkflowAgentBridge } from '../../../../core/workflow/workflow-agent-bridge';
import { SeoKeywordAgent } from '../../../../core/agents/seo-keyword-agent';
import { MarketResearchAgent } from '../../../../core/agents/market-research-agent';
import { ContentStrategyAgent } from '../../../../core/agents/content-strategy-agent';

// Initialize the agent consultation system
let consultationService: DynamicAgentConsultationService | null = null;

function getConsultationService(): DynamicAgentConsultationService {
  if (!consultationService) {
    // Initialize fresh agent system
    const seoAgent = new SeoKeywordAgent();
    const marketAgent = new MarketResearchAgent();
    const strategyAgent = new ContentStrategyAgent();

    // Create agent bridge
    const agentBridge = new WorkflowAgentBridge({
      'seo-keyword': seoAgent,
      'market-research': marketAgent,
      'content-strategy': strategyAgent
    });

    // Create consultation service
    consultationService = new DynamicAgentConsultationService(agentBridge);
  }
  
  return consultationService;
}

/**
 * GET /api/agents/consultation
 * Get agent consultation metrics and status
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'metrics';

    const service = getConsultationService();

    switch (type) {
      case 'metrics':
        const metrics = service.getMetrics();
        return NextResponse.json({
          success: true,
          data: {
            metrics,
            timestamp: new Date().toISOString()
          }
        });

      case 'status':
        // Get agent status through the bridge
        const agentBridge = (service as any).agentBridge;
        const agentStatus = await agentBridge.getAllAgentStatus();
        const healthCheck = await agentBridge.performHealthCheck();
        
        return NextResponse.json({
          success: true,
          data: {
            agents: agentStatus,
            health: healthCheck,
            timestamp: new Date().toISOString()
          }
        });

      case 'history':
        const executionId = searchParams.get('executionId');
        if (executionId) {
          const history = service.getConsultationHistory(executionId);
          return NextResponse.json({
            success: true,
            data: {
              executionId,
              consultations: history,
              timestamp: new Date().toISOString()
            }
          });
        } else {
          const allHistory = service.getAllConsultationHistory();
          return NextResponse.json({
            success: true,
            data: {
              history: allHistory,
              timestamp: new Date().toISOString()
            }
          });
        }

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid type parameter. Use: metrics, status, or history'
        }, { status: 400 });
    }

  } catch (error) {
    console.error('Agent consultation API error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 });
  }
}

/**
 * POST /api/agents/consultation
 * Trigger agent consultation manually
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      agentId, 
      workflowExecutionId, 
      stepId, 
      context,
      consultationConfig 
    } = body;

    // Validate required fields
    if (!agentId || !workflowExecutionId || !stepId || !context) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: agentId, workflowExecutionId, stepId, context'
      }, { status: 400 });
    }

    const service = getConsultationService();

    if (consultationConfig) {
      // Multi-agent consultation
      const results = await service.consultMultipleAgents(
        workflowExecutionId,
        stepId,
        context,
        consultationConfig
      );

      return NextResponse.json({
        success: true,
        data: {
          consultations: results,
          summary: {
            totalConsultations: results.length,
            averageConfidence: results.reduce((sum, r) => sum + r.confidence, 0) / results.length,
            consultedAgents: results.map(r => r.agentId)
          },
          timestamp: new Date().toISOString()
        }
      });
    } else {
      // Single agent consultation
      const result = await service.consultAgent(
        agentId,
        workflowExecutionId,
        stepId,
        context
      );

      return NextResponse.json({
        success: true,
        data: {
          consultation: result,
          timestamp: new Date().toISOString()
        }
      });
    }

  } catch (error) {
    console.error('Agent consultation execution error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Consultation failed'
    }, { status: 500 });
  }
}

/**
 * DELETE /api/agents/consultation
 * Clear consultation metrics and history
 */
export async function DELETE(request: NextRequest) {
  try {
    const service = getConsultationService();
    service.clearMetrics();

    return NextResponse.json({
      success: true,
      message: 'Consultation metrics and history cleared',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Clear consultation data error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to clear data'
    }, { status: 500 });
  }
}
