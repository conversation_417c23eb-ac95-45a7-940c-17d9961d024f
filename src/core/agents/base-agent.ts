/**
 * Base Agent Class for Fresh Dynamic Agent System
 * 
 * Provides common functionality for all specialized agents
 */

import { v4 as uuidv4 } from 'uuid';
import {
  IAgent,
  AgentId,
  AgentCapability,
  AgentConfiguration,
  AgentConsultationRequest,
  AgentConsultationResponse,
  AgentSuggestion,
  ConsultationPriority,
  AgentError
} from './types';

export abstract class BaseAgent implements IAgent {
  protected configuration: AgentConfiguration;
  protected isProcessing: boolean = false;
  protected activeConsultations: Map<string, AgentConsultationRequest> = new Map();

  constructor(
    protected agentId: AgentId,
    protected capabilities: AgentCapability[],
    config?: Partial<AgentConfiguration>
  ) {
    this.configuration = {
      agentId,
      capabilities,
      maxConcurrentConsultations: 3,
      defaultTimeoutMs: 30000,
      retryAttempts: 2,
      enabled: true,
      ...config
    };
  }

  getAgentId(): AgentId {
    return this.agentId;
  }

  getCapabilities(): AgentCapability[] {
    return this.capabilities;
  }

  getConfiguration(): AgentConfiguration {
    return { ...this.configuration };
  }

  updateConfiguration(config: Partial<AgentConfiguration>): void {
    this.configuration = { ...this.configuration, ...config };
  }

  async isAvailable(): Promise<boolean> {
    if (!this.configuration.enabled) {
      return false;
    }

    // Check if we're at max concurrent consultations
    if (this.activeConsultations.size >= this.configuration.maxConcurrentConsultations) {
      return false;
    }

    return true;
  }

  async processConsultation(request: AgentConsultationRequest): Promise<AgentConsultationResponse> {
    const startTime = Date.now();

    try {
      // Validate agent availability
      if (!await this.isAvailable()) {
        throw new AgentError(
          `Agent ${this.agentId} is not available`,
          this.agentId,
          'AGENT_UNAVAILABLE'
        );
      }

      // Add to active consultations
      this.activeConsultations.set(request.id, request);

      // Validate request
      this.validateConsultationRequest(request);

      // Process the consultation (implemented by derived classes)
      const response = await this.executeConsultation(request);

      // Calculate processing time
      const processingTime = Date.now() - startTime;

      // Create standardized response
      const consultationResponse: AgentConsultationResponse = {
        consultationId: request.id,
        agentId: this.agentId,
        response: response.response,
        confidence: response.confidence,
        reasoning: response.reasoning,
        suggestions: response.suggestions || [],
        processingTime: Math.max(processingTime, 1), // Ensure minimum 1ms for testing
        metadata: response.metadata
      };

      return consultationResponse;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      if (error instanceof AgentError) {
        throw error;
      }

      throw new AgentError(
        `Consultation failed: ${error instanceof Error ? error.message : String(error)}`,
        this.agentId,
        'CONSULTATION_FAILED',
        { originalError: error, processingTime }
      );

    } finally {
      // Remove from active consultations
      this.activeConsultations.delete(request.id);
    }
  }

  /**
   * Validate consultation request
   */
  protected validateConsultationRequest(request: AgentConsultationRequest): void {
    if (!request.id) {
      throw new AgentError(
        'Consultation request must have an ID',
        this.agentId,
        'INVALID_REQUEST'
      );
    }

    if (!request.context) {
      throw new AgentError(
        'Consultation request must have context',
        this.agentId,
        'INVALID_REQUEST'
      );
    }

    if (!request.question) {
      throw new AgentError(
        'Consultation request must have a question',
        this.agentId,
        'INVALID_REQUEST'
      );
    }
  }

  /**
   * Execute the actual consultation logic (implemented by derived classes)
   */
  protected abstract executeConsultation(
    request: AgentConsultationRequest
  ): Promise<{
    response: any;
    confidence: number;
    reasoning?: string;
    suggestions?: AgentSuggestion[];
    metadata?: Record<string, any>;
  }>;

  /**
   * Helper method to create suggestions
   */
  protected createSuggestion(
    area: string,
    suggestion: string,
    priority: ConsultationPriority = 'medium',
    confidence?: number,
    actionable: boolean = true
  ): AgentSuggestion {
    return {
      area,
      suggestion,
      priority,
      confidence,
      actionable
    };
  }

  /**
   * Helper method to analyze context complexity
   */
  protected analyzeContextComplexity(context: Record<string, any>): number {
    let complexity = 0;

    // Content length factor
    if (context.content) {
      const contentLength = context.content.length;
      if (contentLength > 2000) complexity += 0.3;
      else if (contentLength > 1000) complexity += 0.2;
      else if (contentLength > 500) complexity += 0.1;
    }

    // Topic complexity factor
    if (context.topic) {
      const technicalKeywords = ['algorithm', 'api', 'framework', 'architecture', 'implementation'];
      const hasTechnicalTerms = technicalKeywords.some(keyword => 
        context.topic.toLowerCase().includes(keyword)
      );
      if (hasTechnicalTerms) complexity += 0.2;
    }

    // Target audience factor
    if (context.targetAudience) {
      const expertAudiences = ['technical experts', 'developers', 'engineers', 'professionals'];
      const isExpertAudience = expertAudiences.some(audience => 
        context.targetAudience.toLowerCase().includes(audience)
      );
      if (isExpertAudience) complexity += 0.2;
    }

    // Multiple goals factor
    if (context.goals && Array.isArray(context.goals) && context.goals.length > 3) {
      complexity += 0.1;
    }

    return Math.min(complexity, 1.0); // Cap at 1.0
  }

  /**
   * Helper method to extract keywords from feedback
   */
  protected extractKeywordsFromFeedback(feedback: string, targetKeywords: string[]): string[] {
    const foundKeywords: string[] = [];
    const feedbackLower = feedback.toLowerCase();

    for (const keyword of targetKeywords) {
      if (feedbackLower.includes(keyword.toLowerCase())) {
        foundKeywords.push(keyword);
      }
    }

    return foundKeywords;
  }

  /**
   * Helper method to calculate confidence based on context quality
   */
  protected calculateConfidence(context: Record<string, any>): number {
    let confidence = 0.5; // Base confidence

    // Required fields present
    if (context.topic) confidence += 0.2;
    if (context.targetAudience) confidence += 0.1;
    if (context.contentType) confidence += 0.1;

    // Additional context
    if (context.primaryKeyword) confidence += 0.05;
    if (context.industry) confidence += 0.05;
    if (context.goals && Array.isArray(context.goals)) confidence += 0.1;

    return Math.min(confidence, 1.0); // Cap at 1.0
  }

  /**
   * Helper method to generate reasoning text
   */
  protected generateReasoning(context: Record<string, any>, analysisPoints: string[]): string {
    const reasoning = [
      `Analysis for ${this.agentId} agent consultation:`,
      `Topic: ${context.topic || 'Not specified'}`,
      `Target Audience: ${context.targetAudience || 'Not specified'}`,
      `Content Type: ${context.contentType || 'Not specified'}`,
      '',
      'Key Analysis Points:',
      ...analysisPoints.map(point => `- ${point}`),
      '',
      `Confidence level based on available context and agent expertise.`
    ];

    return reasoning.join('\n');
  }

  /**
   * Get current status of the agent
   */
  getStatus(): {
    agentId: AgentId;
    isAvailable: boolean;
    activeConsultations: number;
    maxConcurrentConsultations: number;
    enabled: boolean;
  } {
    return {
      agentId: this.agentId,
      isAvailable: this.activeConsultations.size < this.configuration.maxConcurrentConsultations && this.configuration.enabled,
      activeConsultations: this.activeConsultations.size,
      maxConcurrentConsultations: this.configuration.maxConcurrentConsultations,
      enabled: this.configuration.enabled
    };
  }
}
