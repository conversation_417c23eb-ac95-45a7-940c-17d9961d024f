/**
 * Essential Workflow Templates
 * Pre-built templates for common content generation tasks
 */

import { Workflow, WorkflowTemplate, StepType, ArtifactType } from './types';

// Template 1: SEO Blog Post
export const SEO_BLOG_POST_TEMPLATE: WorkflowTemplate = {
  id: 'blog-post-seo',
  name: 'SEO Blog Post',
  description: 'Complete SEO-optimized blog post generation with keyword research and human review',
  workflow: {
    name: 'SEO Blog Post',
    description: 'Generate an SEO-optimized blog post with keyword research',
    version: '1.0.0',
    steps: [
      {
        id: 'topic-input',
        name: 'Topic Input',
        type: StepType.TEXT_INPUT,
        config: {},
        inputs: [],
        outputs: ['topic', 'target_audience', 'primary_keyword'],
        dependencies: []
      },
      {
        id: 'keyword-research',
        name: 'Keyword Research',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            model: 'gpt-4',
            prompt: `Conduct comprehensive keyword research for the topic: "{{topic}}"

Target audience: {{target_audience}}
Primary keyword: {{primary_keyword}}

Please provide:
1. 10-15 related keywords with search intent
2. Long-tail keyword variations
3. Semantic keywords
4. Content structure suggestions
5. Competitor analysis insights

Format the response as JSON with the following structure:
{
  "primary_keywords": [],
  "long_tail_keywords": [],
  "semantic_keywords": [],
  "content_structure": [],
  "competitor_insights": []
}`,
            systemPrompt: 'You are an expert SEO specialist with deep knowledge of keyword research and content optimization.',
            temperature: 0.3,
            maxTokens: 1500
          }
        },
        inputs: ['topic', 'target_audience', 'primary_keyword'],
        outputs: ['keyword_research'],
        dependencies: ['topic-input'],
        consultationConfig: {
          enabled: true,
          triggers: [
            {
              type: 'always',
              agents: ['seo-keyword', 'market-research'],
              priority: 'high'
            },
            {
              type: 'content_complexity',
              condition: { threshold: 0.6 },
              agents: ['content-strategy'],
              priority: 'medium'
            }
          ],
          maxConsultations: 3,
          timeoutMs: 30000,
          fallbackBehavior: 'continue',
          qualityThreshold: 0.7
        }
      },
      {
        id: 'content-creation',
        name: 'Content Creation',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            model: 'gpt-4',
            prompt: `Write a comprehensive, SEO-optimized blog post based on the following:

Topic: {{topic}}
Target Audience: {{target_audience}}
Keyword Research: {{keyword_research}}

Requirements:
- 1500-2000 words
- Include primary and semantic keywords naturally
- Create engaging headlines and subheadings
- Add meta description
- Include call-to-action
- Ensure readability and flow
- Add internal linking suggestions

Structure:
1. Compelling title (H1)
2. Meta description
3. Introduction with hook
4. Main content with H2/H3 subheadings
5. Conclusion with CTA
6. Internal linking suggestions

Format as JSON:
{
  "title": "",
  "meta_description": "",
  "content": "",
  "internal_links": [],
  "word_count": 0
}`,
            systemPrompt: 'You are an expert content writer specializing in SEO-optimized blog posts that engage readers and rank well in search engines.',
            temperature: 0.7,
            maxTokens: 3000
          }
        },
        inputs: ['topic', 'target_audience', 'keyword_research'],
        outputs: ['blog_content'],
        dependencies: ['keyword-research'],
        consultationConfig: {
          enabled: true,
          triggers: [
            {
              type: 'always',
              agents: ['seo-keyword', 'content-strategy'],
              priority: 'high'
            },
            {
              type: 'quality_threshold',
              condition: { threshold: 0.8 },
              agents: ['market-research'],
              priority: 'medium'
            },
            {
              type: 'feedback_keywords',
              condition: { keywords: ['improve', 'better', 'enhance', 'optimize'] },
              agents: ['seo-keyword', 'content-strategy'],
              priority: 'high'
            }
          ],
          maxConsultations: 3,
          timeoutMs: 30000,
          fallbackBehavior: 'continue'
        }
      },
      {
        id: 'human-review',
        name: 'Human Review',
        type: StepType.HUMAN_REVIEW,
        config: {
          reviewConfig: {
            reviewType: 'editing',
            instructions: 'Please review the blog post for accuracy, tone, and SEO optimization. Make any necessary edits.'
          }
        },
        inputs: ['blog_content'],
        outputs: ['approved_content'],
        dependencies: ['content-creation']
      },
      {
        id: 'seo-optimization',
        name: 'SEO Final Check',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            model: 'gpt-3.5-turbo',
            prompt: `Perform a final SEO optimization check on this blog post:

Content: {{approved_content}}
Original Keywords: {{keyword_research}}

Provide:
1. SEO score (1-100)
2. Keyword density analysis
3. Readability score
4. Meta tag suggestions
5. Schema markup recommendations
6. Final optimization suggestions

Format as JSON:
{
  "seo_score": 0,
  "keyword_density": {},
  "readability_score": 0,
  "meta_tags": {},
  "schema_markup": {},
  "optimization_suggestions": []
}`,
            systemPrompt: 'You are an SEO audit specialist focused on technical optimization and search engine ranking factors.',
            temperature: 0.2,
            maxTokens: 1000
          }
        },
        inputs: ['approved_content', 'keyword_research'],
        outputs: ['seo_analysis'],
        dependencies: ['human-review']
      }
    ],
    metadata: {
      category: 'blog',
      tags: ['seo', 'blog', 'content-marketing'],
      difficulty: 'easy',
      estimatedTime: 45
    }
  },
  sampleInputs: {
    topic: 'Best practices for content marketing in 2024',
    target_audience: 'Marketing professionals and business owners',
    primary_keyword: 'content marketing best practices'
  },
  instructions: `This template creates a comprehensive SEO-optimized blog post through the following steps:

1. **Topic Input**: Provide your topic, target audience, and primary keyword
2. **Keyword Research**: AI conducts thorough keyword research and competitor analysis
3. **Content Creation**: AI writes a full blog post optimized for SEO
4. **Human Review**: Review and edit the content for quality and accuracy
5. **SEO Final Check**: AI performs final SEO optimization analysis

The result is a publication-ready blog post optimized for search engines and user engagement.`,
  featured: true
};

// Template 2: Bulk Product Descriptions
export const BULK_PRODUCT_DESCRIPTIONS_TEMPLATE: WorkflowTemplate = {
  id: 'product-descriptions',
  name: 'Bulk Product Descriptions',
  description: 'Generate product descriptions in bulk from CSV data with brand voice consistency',
  workflow: {
    name: 'Bulk Product Descriptions',
    description: 'Generate multiple product descriptions from CSV data',
    version: '1.0.0',
    steps: [
      {
        id: 'csv-import',
        name: 'Import Product Data',
        type: StepType.CSV_IMPORT,
        config: {
          csvConfig: {
            headers: ['product_name', 'category', 'features', 'price', 'target_audience'],
            delimiter: ','
          }
        },
        inputs: [],
        outputs: ['product_data'],
        dependencies: []
      },
      {
        id: 'brand-voice-input',
        name: 'Brand Voice Guidelines',
        type: StepType.TEXT_INPUT,
        config: {},
        inputs: [],
        outputs: ['brand_voice', 'tone', 'key_messages'],
        dependencies: []
      },
      {
        id: 'bulk-generation',
        name: 'Generate Descriptions',
        type: StepType.LOOP,
        config: {
          loopConfig: {
            iterateOver: 'product_data',
            parallelExecution: true,
            maxIterations: 100
          },
          aiConfig: {
            model: 'gpt-3.5-turbo',
            prompt: `Create a compelling product description for:

Product: {{product_name}}
Category: {{category}}
Features: {{features}}
Price: {{price}}
Target Audience: {{target_audience}}

Brand Voice: {{brand_voice}}
Tone: {{tone}}
Key Messages: {{key_messages}}

Requirements:
- 150-200 words
- Highlight key benefits
- Include emotional appeal
- Add call-to-action
- SEO-friendly
- Match brand voice

Format as JSON:
{
  "title": "",
  "description": "",
  "key_features": [],
  "benefits": [],
  "cta": "",
  "seo_keywords": []
}`,
            systemPrompt: 'You are an expert e-commerce copywriter specializing in product descriptions that convert browsers into buyers.',
            temperature: 0.8,
            maxTokens: 800
          }
        },
        inputs: ['product_data', 'brand_voice', 'tone', 'key_messages'],
        outputs: ['product_descriptions'],
        dependencies: ['csv-import', 'brand-voice-input']
      },
      {
        id: 'quality-review',
        name: 'Quality Review',
        type: StepType.HUMAN_REVIEW,
        config: {
          reviewConfig: {
            reviewType: 'approval',
            instructions: 'Review the generated product descriptions for brand consistency and quality. Approve or request changes.'
          }
        },
        inputs: ['product_descriptions'],
        outputs: ['approved_descriptions'],
        dependencies: ['bulk-generation']
      },
      {
        id: 'csv-export',
        name: 'Export Results',
        type: StepType.CSV_EXPORT,
        config: {
          csvConfig: {
            headers: ['product_name', 'title', 'description', 'key_features', 'benefits', 'cta', 'seo_keywords']
          }
        },
        inputs: ['approved_descriptions'],
        outputs: ['export_file'],
        dependencies: ['quality-review']
      }
    ],
    metadata: {
      category: 'ecommerce',
      tags: ['ecommerce', 'bulk', 'product-descriptions'],
      difficulty: 'medium',
      estimatedTime: 120
    }
  },
  sampleInputs: {
    brand_voice: 'Professional, trustworthy, and customer-focused',
    tone: 'Friendly and informative',
    key_messages: 'Quality, value, customer satisfaction'
  },
  instructions: `This template generates product descriptions in bulk from CSV data:

1. **CSV Import**: Upload a CSV file with product information
2. **Brand Voice**: Define your brand voice and messaging guidelines
3. **Bulk Generation**: AI generates descriptions for all products
4. **Quality Review**: Review and approve the generated descriptions
5. **CSV Export**: Download the results as a CSV file

Perfect for e-commerce stores needing to create multiple product descriptions quickly while maintaining brand consistency.`,
  featured: true
};

// Template 3: Content Refresh
export const CONTENT_REFRESH_TEMPLATE: WorkflowTemplate = {
  id: 'content-refresh',
  name: 'Content Refresh & Update',
  description: 'Analyze and update existing content for improved SEO and relevance',
  workflow: {
    name: 'Content Refresh & Update',
    description: 'Update existing content for better performance',
    version: '1.0.0',
    steps: [
      {
        id: 'url-input',
        name: 'Content URL Input',
        type: StepType.TEXT_INPUT,
        config: {},
        inputs: [],
        outputs: ['content_url', 'update_goals'],
        dependencies: []
      },
      {
        id: 'content-fetch',
        name: 'Fetch Existing Content',
        type: StepType.URL_FETCH,
        config: {
          urlConfig: {
            method: 'GET',
            extractContent: true
          }
        },
        inputs: ['content_url'],
        outputs: ['existing_content'],
        dependencies: ['url-input']
      },
      {
        id: 'content-analysis',
        name: 'Analyze Current Content',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            model: 'gpt-4',
            prompt: `Analyze this existing content for improvement opportunities:

Content: {{existing_content}}
Update Goals: {{update_goals}}

Provide analysis on:
1. Content quality and accuracy
2. SEO optimization opportunities
3. Outdated information
4. Missing topics or sections
5. Readability improvements
6. Keyword optimization potential
7. User engagement factors

Format as JSON:
{
  "quality_score": 0,
  "seo_issues": [],
  "outdated_sections": [],
  "missing_topics": [],
  "improvement_opportunities": [],
  "keyword_gaps": [],
  "engagement_issues": []
}`,
            systemPrompt: 'You are a content audit specialist with expertise in SEO, user experience, and content optimization.',
            temperature: 0.3,
            maxTokens: 2000
          }
        },
        inputs: ['existing_content', 'update_goals'],
        outputs: ['content_analysis'],
        dependencies: ['content-fetch']
      },
      {
        id: 'content-update',
        name: 'Generate Updated Content',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            model: 'gpt-4',
            prompt: `Based on the analysis, create an updated version of the content:

Original Content: {{existing_content}}
Analysis: {{content_analysis}}
Update Goals: {{update_goals}}

Requirements:
- Address all identified issues
- Improve SEO optimization
- Update outdated information
- Enhance readability
- Add missing topics
- Maintain original structure where appropriate
- Improve user engagement

Provide the updated content with change notes.

Format as JSON:
{
  "updated_content": "",
  "changes_made": [],
  "seo_improvements": [],
  "new_sections": [],
  "removed_sections": []
}`,
            systemPrompt: 'You are an expert content editor specializing in content optimization and SEO improvement.',
            temperature: 0.6,
            maxTokens: 3000
          }
        },
        inputs: ['existing_content', 'content_analysis', 'update_goals'],
        outputs: ['updated_content'],
        dependencies: ['content-analysis']
      },
      {
        id: 'final-review',
        name: 'Final Review',
        type: StepType.HUMAN_REVIEW,
        config: {
          reviewConfig: {
            reviewType: 'editing',
            instructions: 'Review the updated content. Ensure all changes are accurate and align with your goals.'
          }
        },
        inputs: ['updated_content', 'existing_content'],
        outputs: ['final_content'],
        dependencies: ['content-update']
      }
    ],
    metadata: {
      category: 'seo',
      tags: ['content-refresh', 'seo', 'optimization'],
      difficulty: 'medium',
      estimatedTime: 60
    }
  },
  sampleInputs: {
    content_url: 'https://example.com/blog/old-article',
    update_goals: 'Improve SEO ranking, update statistics, add new insights'
  },
  instructions: `This template refreshes and optimizes existing content:

1. **URL Input**: Provide the URL of content to update and your goals
2. **Content Fetch**: AI extracts the existing content
3. **Content Analysis**: AI analyzes the content for improvement opportunities
4. **Content Update**: AI generates an improved version
5. **Final Review**: Review and approve the updated content

Perfect for maintaining content freshness and improving search rankings.`,
  featured: true
};

// Template 4: How-To Guide (Enhanced)
export const HOW_TO_GUIDE_TEMPLATE: WorkflowTemplate = {
  id: 'how-to-guide',
  name: 'How-To Guide',
  description: 'Step-by-step instructional content with clear actionable steps and troubleshooting',
  workflow: {
    name: 'How-To Guide',
    description: 'Generate comprehensive how-to guides with step-by-step instructions, prerequisites, and troubleshooting',
    version: '2.0.0',
    steps: [
      {
        id: 'topic-input',
        name: 'Topic & Audience Input',
        type: StepType.TEXT_INPUT,
        config: {},
        inputs: [],
        outputs: ['topic', 'target_audience', 'difficulty_level', 'estimated_time'],
        dependencies: []
      },
      {
        id: 'outline-generation',
        name: 'Generate Outline',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            model: 'gpt-4',
            prompt: `Create a detailed outline for a how-to guide on: "{{topic}}"

Target Audience: {{target_audience}}
Difficulty Level: {{difficulty_level}}
Estimated Time: {{estimated_time}}

Generate a comprehensive outline that includes:
1. Introduction with clear objectives
2. Prerequisites and required materials/tools
3. Step-by-step instructions (numbered)
4. Tips and best practices
5. Common mistakes to avoid
6. Troubleshooting section
7. Conclusion with next steps

Format as JSON:
{
  "title": "How to [Topic]",
  "introduction": "Brief introduction explaining what readers will learn",
  "prerequisites": ["List of required items/knowledge"],
  "steps": [
    {
      "stepNumber": 1,
      "title": "Step title",
      "description": "Detailed explanation",
      "tips": ["Helpful tips for this step"]
    }
  ],
  "troubleshooting": [
    {
      "problem": "Common issue",
      "solution": "How to fix it"
    }
  ],
  "conclusion": "Summary and next steps"
}`
          }
        },
        inputs: ['topic', 'target_audience', 'difficulty_level', 'estimated_time'],
        outputs: ['outline'],
        dependencies: ['topic-input']
      },
      {
        id: 'content-generation',
        name: 'Generate Full Content',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            model: 'gpt-4',
            prompt: `Based on this outline, write a complete how-to guide:

{{outline}}

Requirements:
- Write in clear, actionable language
- Use numbered steps for main instructions
- Include helpful tips and warnings where appropriate
- Add estimated time for each major step
- Use bullet points for lists and sub-steps
- Include a materials/tools list at the beginning
- End with a summary and next steps

Target audience: {{target_audience}}
Difficulty level: {{difficulty_level}}

Format the content with proper headings and structure for web publication.`
          }
        },
        inputs: ['outline', 'target_audience', 'difficulty_level'],
        outputs: ['full_content'],
        dependencies: ['outline-generation']
      },
      {
        id: 'human-review',
        name: 'Review How-To Guide',
        type: StepType.HUMAN_REVIEW,
        config: {
          reviewConfig: {
            title: 'How-To Guide Review',
            description: 'Review the generated how-to guide for accuracy and clarity',
            fields: [
              {
                name: 'clarity',
                label: 'Instructions Clarity',
                type: 'rating',
                scale: 5,
                required: true
              },
              {
                name: 'completeness',
                label: 'Content Completeness',
                type: 'rating',
                scale: 5,
                required: true
              },
              {
                name: 'feedback',
                label: 'Feedback & Suggestions',
                type: 'textarea',
                required: false
              }
            ]
          }
        },
        inputs: ['full_content'],
        outputs: ['review_result', 'approved_content'],
        dependencies: ['content-generation']
      }
    ],
    metadata: {
      category: 'Educational',
      difficulty: 'medium',
      estimatedTime: 20
    }
  },
  sampleInputs: {
    topic: 'Set up a home office for remote work',
    target_audience: 'Remote workers and freelancers',
    difficulty_level: 'Beginner',
    estimated_time: '2-3 hours'
  },
  description: `## How-To Guide Template

Perfect for creating step-by-step instructional content that helps users accomplish specific tasks.

### What You'll Get:
1. **Topic Input**: Define your how-to topic and target audience
2. **Outline Generation**: AI creates a structured outline with steps
3. **Content Generation**: AI writes the complete how-to guide
4. **Human Review**: Review and approve the final guide

### Best For:
- Tutorial content
- Process documentation
- Skill-building guides
- Problem-solving instructions

The template ensures clear, actionable instructions with proper structure and helpful tips.`,
  featured: true
};

// Template 5: Listicle
export const LISTICLE_TEMPLATE: WorkflowTemplate = {
  id: 'listicle',
  name: 'Listicle Article',
  description: 'Engaging list-based articles optimized for social sharing',
  workflow: {
    name: 'Listicle Article',
    description: 'Generate engaging list-based content with strong hooks',
    version: '1.0.0',
    steps: [
      {
        id: 'topic-input',
        name: 'Listicle Topic Input',
        type: StepType.TEXT_INPUT,
        config: {},
        inputs: [],
        outputs: ['topic', 'list_count', 'target_audience', 'tone'],
        dependencies: []
      },
      {
        id: 'research-generation',
        name: 'Research & List Items',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            model: 'gpt-4',
            prompt: `Research and create a list of {{list_count}} items for: "{{topic}}"

Target Audience: {{target_audience}}
Tone: {{tone}}

Requirements:
1. Each item should be unique and valuable
2. Items should be ranked or organized logically
3. Include brief explanations for each item
4. Add interesting facts or statistics where relevant
5. Ensure items appeal to the target audience

Format as JSON:
{
  "title": "{{list_count}} [Engaging Title Related to Topic]",
  "introduction": "Hook that explains why this list matters",
  "items": [
    {
      "rank": 1,
      "title": "Item title",
      "description": "Detailed explanation",
      "why_included": "Why this made the list",
      "fun_fact": "Interesting related fact (optional)"
    }
  ],
  "conclusion": "Wrap-up that encourages engagement"
}`
          }
        },
        inputs: ['topic', 'list_count', 'target_audience', 'tone'],
        outputs: ['research_data'],
        dependencies: ['topic-input']
      },
      {
        id: 'content-generation',
        name: 'Write Listicle',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            model: 'gpt-4',
            prompt: `Write an engaging listicle based on this research:

{{research_data}}

Requirements:
- Start with a compelling introduction that hooks readers
- Use engaging subheadings for each list item
- Include relevant details, examples, or statistics
- Add personality and voice appropriate for {{tone}} tone
- Include a strong conclusion that encourages sharing
- Optimize for social media sharing
- Use formatting that's easy to scan

Target audience: {{target_audience}}
Tone: {{tone}}

Make it shareable and engaging while providing real value.`
          }
        },
        inputs: ['research_data', 'target_audience', 'tone'],
        outputs: ['listicle_content'],
        dependencies: ['research-generation']
      },
      {
        id: 'human-review',
        name: 'Review Listicle',
        type: StepType.HUMAN_REVIEW,
        config: {
          reviewConfig: {
            title: 'Listicle Review',
            description: 'Review the listicle for engagement and accuracy',
            fields: [
              {
                name: 'engagement',
                label: 'Engagement Factor',
                type: 'rating',
                scale: 5,
                required: true
              },
              {
                name: 'shareability',
                label: 'Social Shareability',
                type: 'rating',
                scale: 5,
                required: true
              },
              {
                name: 'feedback',
                label: 'Feedback & Improvements',
                type: 'textarea',
                required: false
              }
            ]
          }
        },
        inputs: ['listicle_content'],
        outputs: ['review_result', 'approved_content'],
        dependencies: ['content-generation']
      }
    ],
    metadata: {
      category: 'Social Content',
      difficulty: 'easy',
      estimatedTime: 15
    }
  },
  sampleInputs: {
    topic: 'productivity apps for entrepreneurs',
    list_count: '10',
    target_audience: 'Small business owners and entrepreneurs',
    tone: 'Professional but friendly'
  },
  description: `## Listicle Template

Create engaging, shareable list-based content that performs well on social media.

### What You'll Get:
1. **Topic Input**: Define your list topic and parameters
2. **Research**: AI researches and organizes list items
3. **Content Creation**: AI writes the engaging listicle
4. **Human Review**: Review and approve the final article

### Perfect For:
- Social media content
- Blog traffic generation
- Engagement-focused articles
- Quick-read content

Optimized for shareability and reader engagement.`,
  featured: true
};

// Template 6: Product Review
export const PRODUCT_REVIEW_TEMPLATE: WorkflowTemplate = {
  id: 'product-review',
  name: 'Product Review',
  description: 'Comprehensive product reviews with pros, cons, and recommendations',
  workflow: {
    name: 'Product Review',
    description: 'Generate detailed product reviews with analysis and recommendations',
    version: '1.0.0',
    steps: [
      {
        id: 'product-input',
        name: 'Product Information Input',
        type: StepType.TEXT_INPUT,
        config: {},
        inputs: [],
        outputs: ['product_name', 'product_category', 'price_range', 'target_audience', 'review_focus'],
        dependencies: []
      },
      {
        id: 'research-analysis',
        name: 'Product Research & Analysis',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            model: 'gpt-4',
            prompt: `Conduct comprehensive research and analysis for: "{{product_name}}"

Product Category: {{product_category}}
Price Range: {{price_range}}
Target Audience: {{target_audience}}
Review Focus: {{review_focus}}

Generate a detailed analysis including:
1. Product overview and key features
2. Target market and use cases
3. Competitive landscape
4. Pros and cons analysis
5. Value proposition assessment
6. User experience considerations

Format as JSON:
{
  "overview": "Product description and positioning",
  "key_features": ["List of main features"],
  "target_market": "Who this product is for",
  "competitors": ["Main competing products"],
  "pros": ["Positive aspects"],
  "cons": ["Negative aspects or limitations"],
  "use_cases": ["Specific scenarios where product excels"],
  "value_assessment": "Overall value for money analysis"
}`
          }
        },
        inputs: ['product_name', 'product_category', 'price_range', 'target_audience', 'review_focus'],
        outputs: ['product_analysis'],
        dependencies: ['product-input']
      },
      {
        id: 'review-generation',
        name: 'Write Product Review',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            model: 'gpt-4',
            prompt: `Write a comprehensive product review based on this analysis:

{{product_analysis}}

Product: {{product_name}}
Target Audience: {{target_audience}}
Review Focus: {{review_focus}}

Requirements:
- Start with an engaging introduction
- Include detailed feature analysis
- Provide balanced pros and cons
- Add real-world use case examples
- Include a clear recommendation
- End with a summary and rating
- Use subheadings for easy scanning
- Write in an authoritative but accessible tone

Structure:
1. Introduction and first impressions
2. Key features breakdown
3. Performance analysis
4. Pros and cons
5. Who should buy this
6. Final verdict and rating`
          }
        },
        inputs: ['product_analysis', 'product_name', 'target_audience', 'review_focus'],
        outputs: ['review_content'],
        dependencies: ['research-analysis']
      },
      {
        id: 'human-review',
        name: 'Review Product Review',
        type: StepType.HUMAN_REVIEW,
        config: {
          reviewConfig: {
            title: 'Product Review Review',
            description: 'Review the product review for accuracy and helpfulness',
            fields: [
              {
                name: 'accuracy',
                label: 'Information Accuracy',
                type: 'rating',
                scale: 5,
                required: true
              },
              {
                name: 'helpfulness',
                label: 'Reader Helpfulness',
                type: 'rating',
                scale: 5,
                required: true
              },
              {
                name: 'balance',
                label: 'Balanced Perspective',
                type: 'rating',
                scale: 5,
                required: true
              },
              {
                name: 'feedback',
                label: 'Feedback & Corrections',
                type: 'textarea',
                required: false
              }
            ]
          }
        },
        inputs: ['review_content'],
        outputs: ['review_result', 'approved_content'],
        dependencies: ['review-generation']
      }
    ],
    metadata: {
      category: 'Review',
      difficulty: 'medium',
      estimatedTime: 25
    }
  },
  sampleInputs: {
    product_name: 'Apple MacBook Air M2',
    product_category: 'Laptop computers',
    price_range: '$1000-$1500',
    target_audience: 'Creative professionals and students',
    review_focus: 'Performance and portability for creative work'
  },
  description: `## Product Review Template

Create comprehensive, balanced product reviews that help readers make informed decisions.

### What You'll Get:
1. **Product Input**: Define the product and review parameters
2. **Research & Analysis**: AI researches features and competition
3. **Review Writing**: AI creates a detailed, balanced review
4. **Human Review**: Review and approve the final content

### Perfect For:
- Affiliate marketing content
- Product comparison articles
- Consumer guidance content
- E-commerce product pages

Generates trustworthy reviews with balanced pros/cons analysis.`,
  featured: true
};

// Template 7: Email Campaign
export const EMAIL_CAMPAIGN_TEMPLATE: WorkflowTemplate = {
  id: 'email-campaign',
  name: 'Email Campaign',
  description: 'Engaging email marketing campaigns with strong CTAs',
  workflow: {
    name: 'Email Campaign',
    description: 'Generate effective email marketing campaigns',
    version: '1.0.0',
    steps: [
      {
        id: 'campaign-input',
        name: 'Campaign Details Input',
        type: StepType.TEXT_INPUT,
        config: {},
        inputs: [],
        outputs: ['campaign_goal', 'target_audience', 'email_type', 'brand_voice', 'cta_objective'],
        dependencies: []
      },
      {
        id: 'strategy-development',
        name: 'Email Strategy Development',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            model: 'gpt-4',
            prompt: `Develop an email marketing strategy for:

Campaign Goal: {{campaign_goal}}
Target Audience: {{target_audience}}
Email Type: {{email_type}}
Brand Voice: {{brand_voice}}
CTA Objective: {{cta_objective}}

Create a strategy that includes:
1. Subject line options (A/B test ready)
2. Email structure and flow
3. Key messaging points
4. CTA placement and wording
5. Personalization opportunities

Format as JSON:
{
  "subject_lines": ["3-5 compelling subject line options"],
  "email_structure": {
    "opening": "How to start the email",
    "body_sections": ["Main content sections"],
    "cta_placement": "Where and how to place CTAs",
    "closing": "How to end the email"
  },
  "key_messages": ["Core points to communicate"],
  "cta_options": ["Different CTA text options"],
  "personalization": ["Ways to personalize the email"]
}`
          }
        },
        inputs: ['campaign_goal', 'target_audience', 'email_type', 'brand_voice', 'cta_objective'],
        outputs: ['email_strategy'],
        dependencies: ['campaign-input']
      },
      {
        id: 'email-generation',
        name: 'Write Email Content',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            model: 'gpt-4',
            prompt: `Write a complete email campaign based on this strategy:

{{email_strategy}}

Campaign Details:
- Goal: {{campaign_goal}}
- Audience: {{target_audience}}
- Type: {{email_type}}
- Voice: {{brand_voice}}
- CTA: {{cta_objective}}

Requirements:
- Compelling subject line
- Engaging opening that hooks readers
- Clear, scannable content structure
- Strong, action-oriented CTAs
- Professional but personable tone
- Mobile-friendly formatting
- Include preheader text

Provide multiple subject line options for A/B testing.`
          }
        },
        inputs: ['email_strategy', 'campaign_goal', 'target_audience', 'email_type', 'brand_voice', 'cta_objective'],
        outputs: ['email_content'],
        dependencies: ['strategy-development']
      },
      {
        id: 'human-review',
        name: 'Review Email Campaign',
        type: StepType.HUMAN_REVIEW,
        config: {
          reviewConfig: {
            title: 'Email Campaign Review',
            description: 'Review the email campaign for effectiveness and brand alignment',
            fields: [
              {
                name: 'effectiveness',
                label: 'Campaign Effectiveness',
                type: 'rating',
                scale: 5,
                required: true
              },
              {
                name: 'brand_alignment',
                label: 'Brand Voice Alignment',
                type: 'rating',
                scale: 5,
                required: true
              },
              {
                name: 'cta_strength',
                label: 'CTA Strength',
                type: 'rating',
                scale: 5,
                required: true
              },
              {
                name: 'feedback',
                label: 'Feedback & Adjustments',
                type: 'textarea',
                required: false
              }
            ]
          }
        },
        inputs: ['email_content'],
        outputs: ['review_result', 'approved_content'],
        dependencies: ['email-generation']
      }
    ],
    metadata: {
      category: 'Marketing',
      difficulty: 'medium',
      estimatedTime: 20
    }
  },
  sampleInputs: {
    campaign_goal: 'Promote new product launch',
    target_audience: 'Existing customers who purchased similar products',
    email_type: 'Product announcement',
    brand_voice: 'Friendly and enthusiastic',
    cta_objective: 'Drive pre-orders for new product'
  },
  description: `## Email Campaign Template

Create effective email marketing campaigns that engage subscribers and drive conversions.

### What You'll Get:
1. **Campaign Input**: Define goals and target audience
2. **Strategy Development**: AI creates email marketing strategy
3. **Content Creation**: AI writes the complete email campaign
4. **Human Review**: Review and approve the final campaign

### Perfect For:
- Product launches
- Newsletter content
- Promotional campaigns
- Customer retention emails

Optimized for engagement and conversion with strong CTAs.`,
  featured: false
};

// Template 8: Case Study Template
export const CASE_STUDY_TEMPLATE: WorkflowTemplate = {
  id: 'case-study',
  name: 'Case Study',
  description: 'In-depth case studies showcasing real-world results and success stories',
  workflow: {
    name: 'Case Study',
    description: 'Generate comprehensive case studies with problem-solution-results structure',
    version: '1.0.0',
    steps: [
      {
        id: 'case-input',
        name: 'Case Study Details Input',
        type: StepType.TEXT_INPUT,
        config: {},
        inputs: [],
        outputs: ['subject', 'industry', 'challenge', 'solution_overview', 'target_audience'],
        dependencies: []
      },
      {
        id: 'research-analysis',
        name: 'Research & Analysis',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            model: 'gpt-4',
            prompt: `Conduct thorough research and analysis for a case study about: "{{subject}}" in the {{industry}} industry.

Challenge: {{challenge}}
Solution Overview: {{solution_overview}}
Target Audience: {{target_audience}}

Research and analyze:
1. Industry context and background
2. Stakeholder analysis
3. Problem scope and impact
4. Solution methodology
5. Success metrics and KPIs
6. Lessons learned and best practices

Format as JSON:
{
  "industry_context": "Background information about the industry",
  "stakeholder_analysis": ["Key stakeholders involved"],
  "problem_scope": "Detailed problem analysis",
  "solution_methodology": "How the solution was implemented",
  "success_metrics": ["Key performance indicators"],
  "lessons_learned": ["Key takeaways and insights"]
}`,
            systemPrompt: 'You are a business analyst specializing in case study research and analysis.',
            temperature: 0.3,
            maxTokens: 2000
          }
        },
        inputs: ['subject', 'industry', 'challenge', 'solution_overview', 'target_audience'],
        outputs: ['research_analysis'],
        dependencies: ['case-input']
      },
      {
        id: 'case-study-generation',
        name: 'Generate Case Study',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            model: 'gpt-4',
            prompt: `Write a comprehensive case study based on this research:

{{research_analysis}}

Subject: {{subject}}
Industry: {{industry}}
Target Audience: {{target_audience}}

Structure the case study with:
1. Executive Summary
2. Background & Challenge
3. Solution Implementation
4. Results & Impact
5. Lessons Learned
6. Conclusion & Next Steps

Include specific data, quotes, and measurable outcomes. Make it engaging and credible.

Format as JSON:
{
  "title": "Compelling case study title",
  "executive_summary": "Brief overview of the case",
  "background": "Context and challenge description",
  "solution": "Detailed solution implementation",
  "results": "Specific outcomes and metrics",
  "lessons_learned": "Key insights and takeaways",
  "conclusion": "Summary and future implications",
  "word_count": 0
}`,
            systemPrompt: 'You are an expert business writer specializing in compelling case studies that demonstrate clear value and results.',
            temperature: 0.7,
            maxTokens: 3500
          }
        },
        inputs: ['research_analysis', 'subject', 'industry', 'target_audience'],
        outputs: ['case_study_content'],
        dependencies: ['research-analysis']
      },
      {
        id: 'human-review',
        name: 'Human Review',
        type: StepType.HUMAN_REVIEW,
        config: {
          reviewConfig: {
            reviewType: 'editing',
            instructions: 'Review the case study for accuracy, credibility, and impact. Verify data and ensure the story is compelling.'
          }
        },
        inputs: ['case_study_content'],
        outputs: ['approved_content'],
        dependencies: ['case-study-generation']
      }
    ],
    metadata: {
      category: 'Business',
      difficulty: 'hard',
      estimatedTime: 45
    }
  },
  sampleInputs: {
    subject: 'Digital transformation at mid-size manufacturing company',
    industry: 'Manufacturing',
    challenge: 'Outdated systems causing inefficiencies and quality issues',
    solution_overview: 'Implemented cloud-based ERP and IoT monitoring',
    target_audience: 'Manufacturing executives and IT decision makers'
  },
  instructions: `## Case Study Template

Create compelling case studies that showcase real-world success stories and measurable results.

### What You'll Get:
1. **Case Details Input**: Define subject, industry, and key details
2. **Research & Analysis**: AI conducts thorough background research
3. **Case Study Generation**: AI writes the complete case study
4. **Human Review**: Review and approve the final case study

### Perfect For:
- Customer success stories
- Business transformation examples
- Product implementation showcases
- Industry best practices

Optimized for credibility and demonstrating clear ROI.`,
  featured: true
};

// Template 9: Opinion Piece Template
export const OPINION_PIECE_TEMPLATE: WorkflowTemplate = {
  id: 'opinion-piece',
  name: 'Opinion Piece',
  description: 'Thought-provoking opinion articles that establish thought leadership',
  workflow: {
    name: 'Opinion Piece',
    description: 'Generate compelling opinion pieces with strong arguments and evidence',
    version: '1.0.0',
    steps: [
      {
        id: 'opinion-input',
        name: 'Opinion Topic Input',
        type: StepType.TEXT_INPUT,
        config: {},
        inputs: [],
        outputs: ['topic', 'stance', 'target_audience', 'tone', 'key_arguments'],
        dependencies: []
      },
      {
        id: 'argument-research',
        name: 'Research Arguments & Evidence',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            model: 'gpt-4',
            prompt: `Research and develop arguments for an opinion piece on: "{{topic}}"

Stance: {{stance}}
Target Audience: {{target_audience}}
Key Arguments: {{key_arguments}}

Research and develop:
1. Supporting evidence and data
2. Counter-arguments and rebuttals
3. Expert opinions and quotes
4. Current trends and context
5. Real-world examples
6. Logical argument structure

Format as JSON:
{
  "supporting_evidence": ["Strong evidence supporting the stance"],
  "counter_arguments": ["Potential opposing viewpoints"],
  "rebuttals": ["Responses to counter-arguments"],
  "expert_opinions": ["Relevant expert quotes or studies"],
  "current_context": "Industry/market context",
  "examples": ["Real-world examples supporting the stance"],
  "argument_structure": ["Logical flow of arguments"]
}`,
            systemPrompt: 'You are a research analyst specializing in developing well-supported arguments for opinion pieces.',
            temperature: 0.3,
            maxTokens: 2000
          }
        },
        inputs: ['topic', 'stance', 'target_audience', 'key_arguments'],
        outputs: ['argument_research'],
        dependencies: ['opinion-input']
      },
      {
        id: 'opinion-generation',
        name: 'Write Opinion Piece',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            model: 'gpt-4',
            prompt: `Write a compelling opinion piece based on this research:

{{argument_research}}

Topic: {{topic}}
Stance: {{stance}}
Target Audience: {{target_audience}}
Tone: {{tone}}

Structure the opinion piece with:
1. Compelling headline and hook
2. Clear thesis statement
3. Supporting arguments with evidence
4. Address counter-arguments
5. Strong conclusion with call to action

Make it persuasive, well-reasoned, and engaging.

Format as JSON:
{
  "title": "Compelling headline that captures the stance",
  "introduction": "Hook and thesis statement",
  "main_arguments": "Core arguments with supporting evidence",
  "counter_argument_section": "Address opposing views",
  "conclusion": "Strong conclusion with call to action",
  "word_count": 0
}`,
            systemPrompt: 'You are an expert opinion writer who creates persuasive, well-reasoned articles that establish thought leadership.',
            temperature: 0.7,
            maxTokens: 3000
          }
        },
        inputs: ['argument_research', 'topic', 'stance', 'target_audience', 'tone'],
        outputs: ['opinion_content'],
        dependencies: ['argument-research']
      },
      {
        id: 'human-review',
        name: 'Human Review',
        type: StepType.HUMAN_REVIEW,
        config: {
          reviewConfig: {
            reviewType: 'editing',
            instructions: 'Review the opinion piece for logical flow, persuasiveness, and accuracy. Ensure arguments are well-supported.'
          }
        },
        inputs: ['opinion_content'],
        outputs: ['approved_content'],
        dependencies: ['opinion-generation']
      }
    ],
    metadata: {
      category: 'Editorial',
      difficulty: 'hard',
      estimatedTime: 35
    }
  },
  sampleInputs: {
    topic: 'The future of remote work in enterprise organizations',
    stance: 'Hybrid work models are more effective than fully remote or fully in-office',
    target_audience: 'Business executives and HR leaders',
    tone: 'Professional and persuasive',
    key_arguments: 'Productivity data, employee satisfaction, cost considerations'
  },
  instructions: `## Opinion Piece Template

Create thought-provoking opinion articles that establish thought leadership and drive meaningful discussions.

### What You'll Get:
1. **Topic Input**: Define your stance and key arguments
2. **Research & Evidence**: AI gathers supporting data and counter-arguments
3. **Opinion Generation**: AI writes the complete opinion piece
4. **Human Review**: Review and approve the final article

### Perfect For:
- Thought leadership content
- Industry commentary
- Trend analysis
- Position statements

Optimized for credibility and persuasive impact.`,
  featured: true
};

// Template 10: Technical Tutorial Template
export const TECHNICAL_TUTORIAL_TEMPLATE: WorkflowTemplate = {
  id: 'technical-tutorial',
  name: 'Technical Tutorial',
  description: 'Comprehensive technical tutorials with code examples and best practices',
  workflow: {
    name: 'Technical Tutorial',
    description: 'Generate detailed technical tutorials with step-by-step instructions and code examples',
    version: '1.0.0',
    steps: [
      {
        id: 'tutorial-input',
        name: 'Tutorial Details Input',
        type: StepType.TEXT_INPUT,
        config: {},
        inputs: [],
        outputs: ['technology', 'skill_level', 'learning_objectives', 'target_audience', 'prerequisites'],
        dependencies: []
      },
      {
        id: 'curriculum-design',
        name: 'Design Tutorial Curriculum',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            model: 'gpt-4',
            prompt: `Design a comprehensive curriculum for a technical tutorial on: "{{technology}}"

Skill Level: {{skill_level}}
Learning Objectives: {{learning_objectives}}
Target Audience: {{target_audience}}
Prerequisites: {{prerequisites}}

Create a detailed curriculum including:
1. Learning path structure
2. Key concepts to cover
3. Practical exercises
4. Code examples needed
5. Common pitfalls to address
6. Assessment criteria

Format as JSON:
{
  "learning_path": ["Sequential steps in the learning journey"],
  "key_concepts": ["Core concepts to master"],
  "practical_exercises": ["Hands-on exercises and projects"],
  "code_examples": ["Types of code examples needed"],
  "common_pitfalls": ["Typical mistakes and how to avoid them"],
  "assessment_criteria": ["How to measure learning success"],
  "estimated_duration": "Time needed to complete"
}`,
            systemPrompt: 'You are an expert technical educator specializing in creating effective learning curricula for developers.',
            temperature: 0.3,
            maxTokens: 2000
          }
        },
        inputs: ['technology', 'skill_level', 'learning_objectives', 'target_audience', 'prerequisites'],
        outputs: ['curriculum_design'],
        dependencies: ['tutorial-input']
      },
      {
        id: 'tutorial-generation',
        name: 'Generate Technical Tutorial',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            model: 'gpt-4',
            prompt: `Write a comprehensive technical tutorial based on this curriculum:

{{curriculum_design}}

Technology: {{technology}}
Skill Level: {{skill_level}}
Target Audience: {{target_audience}}

Structure the tutorial with:
1. Introduction and prerequisites
2. Step-by-step instructions
3. Code examples with explanations
4. Best practices and tips
5. Troubleshooting section
6. Next steps and resources

Include working code examples, clear explanations, and practical applications.

Format as JSON:
{
  "title": "Clear, descriptive tutorial title",
  "introduction": "Overview and what readers will learn",
  "prerequisites": "Required knowledge and setup",
  "tutorial_sections": [
    {
      "section_title": "Section name",
      "content": "Detailed explanation",
      "code_examples": ["Working code snippets"],
      "tips": ["Best practices and tips"]
    }
  ],
  "troubleshooting": "Common issues and solutions",
  "conclusion": "Summary and next steps",
  "word_count": 0
}`,
            systemPrompt: 'You are an expert technical writer who creates clear, practical tutorials that help developers learn effectively.',
            temperature: 0.7,
            maxTokens: 4000
          }
        },
        inputs: ['curriculum_design', 'technology', 'skill_level', 'target_audience'],
        outputs: ['tutorial_content'],
        dependencies: ['curriculum-design']
      },
      {
        id: 'human-review',
        name: 'Human Review',
        type: StepType.HUMAN_REVIEW,
        config: {
          reviewConfig: {
            reviewType: 'technical',
            instructions: 'Review the tutorial for technical accuracy, clarity, and completeness. Test code examples if possible.'
          }
        },
        inputs: ['tutorial_content'],
        outputs: ['approved_content'],
        dependencies: ['tutorial-generation']
      }
    ],
    metadata: {
      category: 'Technical',
      difficulty: 'hard',
      estimatedTime: 60
    }
  },
  sampleInputs: {
    technology: 'React Hooks for state management',
    skill_level: 'Intermediate',
    learning_objectives: 'Master useState, useEffect, and custom hooks',
    target_audience: 'React developers looking to improve state management skills',
    prerequisites: 'Basic React knowledge, JavaScript ES6+'
  },
  instructions: `## Technical Tutorial Template

Create comprehensive technical tutorials that help developers learn new technologies and best practices.

### What You'll Get:
1. **Tutorial Input**: Define technology, skill level, and objectives
2. **Curriculum Design**: AI creates structured learning path
3. **Tutorial Generation**: AI writes complete tutorial with code examples
4. **Human Review**: Review for technical accuracy and clarity

### Perfect For:
- Programming tutorials
- Framework guides
- Tool documentation
- Best practices guides

Optimized for developer learning and practical application.`,
  featured: true
};

// Template with Human Approval: SEO Blog Post with Human Approval
export const SEO_BLOG_POST_WITH_APPROVAL_TEMPLATE: WorkflowTemplate = {
  id: 'blog-post-seo-approval',
  name: 'SEO Blog Post with Human Approval',
  description: 'AI generates content, humans approve at key stages to ensure quality',
  workflow: {
    name: 'SEO Blog Post with Approval',
    description: 'Generate an SEO-optimized blog post with approval gates for keywords and content',
    version: '1.0.0',
    steps: [
      {
        id: 'topic-input',
        name: 'Topic Input',
        type: StepType.TEXT_INPUT,
        config: {},
        inputs: [],
        outputs: ['topic', 'target_audience', 'primary_keyword'],
        dependencies: []
      },
      {
        id: 'keyword-research',
        name: 'Keyword Research',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            model: 'gpt-4',
            prompt: `Conduct comprehensive keyword research for the topic: "{{topic}}"

Target audience: {{target_audience}}
Primary keyword: {{primary_keyword}}

Provide:
1. 10-15 related keywords with search volume estimates
2. Long-tail keyword opportunities
3. Competitor analysis keywords
4. Content gap analysis
5. Keyword difficulty assessment

Format as JSON:
{
  "primary_keywords": ["keyword1", "keyword2"],
  "long_tail_keywords": ["long tail 1", "long tail 2"],
  "related_keywords": ["related1", "related2"],
  "competitor_keywords": ["comp1", "comp2"],
  "content_gaps": ["gap1", "gap2"],
  "keyword_strategy": "Overall strategy description"
}`
          }
        },
        inputs: ['topic', 'target_audience', 'primary_keyword'],
        outputs: ['keyword_research'],
        dependencies: ['topic-input']
      },
      {
        id: 'keyword-approval',
        name: 'Human Approval: Keyword Research',
        type: StepType.APPROVAL_GATE,
        config: {
          reviewConfig: {
            reviewType: 'approval',
            instructions: 'AI has generated keyword research. Please review and approve to continue with content creation, or reject to stop the workflow.',
            deadline: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
          }
        },
        inputs: ['keyword_research'],
        outputs: ['approved_keywords'],
        dependencies: ['keyword-research']
      },
      {
        id: 'content-creation',
        name: 'Content Creation',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            model: 'gpt-4',
            prompt: `Write a complete SEO-optimized blog post for: "{{topic}}"

Using approved keywords: {{approved_keywords}}
Target audience: {{target_audience}}

Requirements:
- 1500-2000 words
- Include approved keywords naturally
- Engaging and informative tone
- Proper heading structure (H1, H2, H3)
- Include internal linking opportunities
- Add a compelling introduction and conclusion

Format as JSON:
{
  "title": "Final blog post title",
  "meta_description": "SEO meta description",
  "content": "Full blog post content with HTML formatting",
  "word_count": 1500,
  "keywords_used": ["keyword1", "keyword2"]
}`
          }
        },
        inputs: ['topic', 'target_audience', 'approved_keywords'],
        outputs: ['blog_content'],
        dependencies: ['keyword-approval']
      },
      {
        id: 'content-approval',
        name: 'Human Approval: Final Content',
        type: StepType.APPROVAL_GATE,
        config: {
          reviewConfig: {
            reviewType: 'approval',
            instructions: 'AI has generated the final blog post. Please review for quality, accuracy, and SEO optimization. Approve to complete the workflow or reject to stop.',
            deadline: new Date(Date.now() + 48 * 60 * 60 * 1000).toISOString()
          }
        },
        inputs: ['blog_content'],
        outputs: ['approved_content'],
        dependencies: ['content-creation']
      }
    ],
    metadata: {
      category: 'Content Marketing',
      difficulty: 'medium',
      estimatedTime: 180,
      tags: ['SEO', 'Blog', 'Content Marketing', 'Approval Workflow'],
      featured: true
    }
  },
  sampleInputs: {
    topic: 'How to Optimize Website Performance for Better SEO',
    target_audience: 'Web developers and digital marketers',
    primary_keyword: 'website performance optimization'
  },
  instructions: 'This template creates an SEO-optimized blog post with human approval gates at critical stages. The workflow will pause for human review after keyword research and content creation, ensuring quality control before proceeding.',
  featured: true
};

// Export all templates
export const ESSENTIAL_TEMPLATES: WorkflowTemplate[] = [
  SEO_BLOG_POST_TEMPLATE,
  SEO_BLOG_POST_WITH_APPROVAL_TEMPLATE,
  BULK_PRODUCT_DESCRIPTIONS_TEMPLATE,
  CONTENT_REFRESH_TEMPLATE,
  HOW_TO_GUIDE_TEMPLATE,
  LISTICLE_TEMPLATE,
  PRODUCT_REVIEW_TEMPLATE,
  EMAIL_CAMPAIGN_TEMPLATE,
  CASE_STUDY_TEMPLATE,
  OPINION_PIECE_TEMPLATE,
  TECHNICAL_TUTORIAL_TEMPLATE
];

// Enhanced Blog Templates (subset for blog-specific use)
export const ENHANCED_BLOG_TEMPLATES: WorkflowTemplate[] = [
  SEO_BLOG_POST_TEMPLATE,
  HOW_TO_GUIDE_TEMPLATE,
  LISTICLE_TEMPLATE,
  CASE_STUDY_TEMPLATE,
  OPINION_PIECE_TEMPLATE,
  TECHNICAL_TUTORIAL_TEMPLATE
];

// Template registry for easy access
export class TemplateRegistry {
  private templates = new Map<string, WorkflowTemplate>();

  constructor() {
    // Register essential templates
    ESSENTIAL_TEMPLATES.forEach(template => {
      this.templates.set(template.id, template);
    });
  }

  getTemplate(id: string): WorkflowTemplate | null {
    return this.templates.get(id) || null;
  }

  getAllTemplates(): WorkflowTemplate[] {
    return Array.from(this.templates.values());
  }

  getFeaturedTemplates(): WorkflowTemplate[] {
    return Array.from(this.templates.values()).filter(t => t.featured);
  }

  getTemplatesByCategory(category: string): WorkflowTemplate[] {
    return Array.from(this.templates.values())
      .filter(t => t.workflow.metadata.category === category);
  }

  registerTemplate(template: WorkflowTemplate): void {
    this.templates.set(template.id, template);
  }
}

// Export a singleton instance
export const templateRegistry = new TemplateRegistry();
