/**
 * Workflow Execution Component with Agent Consultation
 *
 * Execute workflows with real-time agent consultation monitoring and backend integration
 */

'use client';

import { useState, useEffect, useRef } from 'react';

interface WorkflowStep {
  id: string;
  name: string;
  type: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  startedAt?: string;
  completedAt?: string;
  duration?: number;
  consultationConfig?: {
    enabled: boolean;
    triggers: any[];
  };
  consultationResults?: Array<{
    agentId: string;
    confidence: number;
    suggestions: any[];
    processingTime: number;
  }>;
  output?: any;
  error?: string;
}

interface ExecutionStatus {
  executionId: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  currentStepIndex: number;
  steps: WorkflowStep[];
  startedAt: string;
  completedAt?: string;
  totalDuration?: number;
  agentConsultations: number;
  successfulConsultations: number;
  failedConsultations: number;
}

interface Props {
  workflow: any;
  onExecutionStart: (executionId: string) => void;
  onNotification: (message: string) => void;
  enableAgentConsultation?: boolean;
}

export default function WorkflowExecution({
  workflow,
  onExecutionStart,
  onNotification,
  enableAgentConsultation = true
}: Props) {
  const [inputs, setInputs] = useState<Record<string, any>>({});
  const [executionStatus, setExecutionStatus] = useState<ExecutionStatus | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [executionLogs, setExecutionLogs] = useState<Array<{
    timestamp: string;
    level: 'info' | 'warning' | 'error' | 'success';
    message: string;
    stepId?: string;
    agentId?: string;
  }>>([]);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (workflow) {
      initializeInputs();
    }
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [workflow]);

  useEffect(() => {
    validateInputs();
  }, [inputs, workflow]);

  const initializeInputs = () => {
    const initialInputs: Record<string, any> = {};

    // Initialize based on workflow template sample inputs
    if (workflow.template?.sampleInputs) {
      Object.entries(workflow.template.sampleInputs).forEach(([key, value]) => {
        initialInputs[key] = value;
      });
    }

    // Add common inputs
    if (!initialInputs.topic) initialInputs.topic = '';
    if (!initialInputs.target_audience) initialInputs.target_audience = '';
    if (!initialInputs.primary_keyword) initialInputs.primary_keyword = '';

    setInputs(initialInputs);
  };

  const validateInputs = () => {
    const errors: string[] = [];

    if (!inputs.topic?.trim()) {
      errors.push('Topic is required');
    }

    if (!inputs.target_audience?.trim()) {
      errors.push('Target audience is required');
    }

    // Validate based on workflow requirements
    if (workflow?.steps) {
      workflow.steps.forEach((step: any) => {
        step.inputs?.forEach((inputKey: string) => {
          if (!inputs[inputKey]?.trim()) {
            errors.push(`${inputKey.replace('_', ' ')} is required for step: ${step.name}`);
          }
        });
      });
    }

    setValidationErrors(errors);
  };

  const handleStartExecution = async () => {
    if (validationErrors.length > 0) {
      onNotification('Please fix validation errors before starting execution');
      return;
    }

    try {
      setIsExecuting(true);
      addLog('info', 'Creating real workflow execution...');

      // Create real workflow using the workflow engine API
      const createResponse = await fetch('/api/workflow/create-enhanced', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: workflow?.name || 'Agent-Enhanced Workflow',
          description: workflow?.description || 'Workflow with agent consultation',
          steps: workflow?.steps || [],
          template: workflow?.template || 'custom',
          agentConsultationEnabled: enableAgentConsultation,
          metadata: {
            source: 'agent-enhanced-ui',
            inputs: inputs
          }
        })
      });

      if (!createResponse.ok) {
        throw new Error(`Failed to create workflow: ${createResponse.statusText}`);
      }

      const createResult = await createResponse.json();
      if (!createResult.success) {
        throw new Error(createResult.error || 'Failed to create workflow');
      }

      const workflowId = createResult.data.workflowId;
      addLog('success', `Workflow created: ${workflowId}`);

      // Execute the workflow
      const executeResponse = await fetch('/api/workflow/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          workflowId,
          inputs,
          metadata: {
            userId: 'agent-enhanced-user',
            source: 'agent-enhanced-ui',
            enableAgentConsultation
          }
        })
      });

      if (!executeResponse.ok) {
        throw new Error(`Failed to execute workflow: ${executeResponse.statusText}`);
      }

      const executeResult = await executeResponse.json();
      if (!executeResult.success) {
        throw new Error(executeResult.error || 'Failed to execute workflow');
      }

      const executionId = executeResult.data.executionId;
      addLog('success', `Real workflow execution started: ${executionId}`);
      onExecutionStart(executionId);
      onNotification(`Workflow execution started: ${executionId.slice(-8)}`);

      // Initialize execution status with real ID
      const initialStatus: ExecutionStatus = {
        executionId,
        status: 'running',
        currentStepIndex: 0,
        steps: (workflow?.steps || []).map((step: any) => ({
          id: step.id,
          name: step.name,
          type: step.type,
          status: 'pending',
          consultationConfig: step.consultationConfig
        })),
        startedAt: new Date().toISOString(),
        agentConsultations: 0,
        successfulConsultations: 0,
        failedConsultations: 0
      };

      setExecutionStatus(initialStatus);

      // Start monitoring the real execution
      await monitorRealExecution(executionId);

    } catch (error) {
      console.error('Failed to start workflow execution:', error);
      setIsExecuting(false);
      addLog('error', `Failed to start workflow execution: ${error instanceof Error ? error.message : 'Unknown error'}`);
      onNotification('Failed to start workflow execution');
    }
  };

  const monitorRealExecution = async (executionId: string) => {
    try {
      // Start polling for real execution updates
      intervalRef.current = setInterval(async () => {
        await updateRealExecutionStatus(executionId);
      }, 3000);

      addLog('info', 'Monitoring real workflow execution...');

    } catch (error) {
      console.error('Workflow monitoring failed:', error);
      addLog('error', 'Workflow monitoring failed');
      onNotification('Workflow monitoring failed');
    }
  };

  const updateRealExecutionStatus = async (executionId: string) => {
    try {
      const response = await fetch(`/api/workflow/execution/${executionId}`);
      if (!response.ok) {
        if (response.status === 404) {
          addLog('warning', 'Execution not found - may still be initializing');
          return;
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      if (result.success && result.data) {
        const { execution, steps } = result.data;

        // Update execution status
        setExecutionStatus(prev => prev ? {
          ...prev,
          status: execution.status,
          progress: execution.progress || 0,
          steps: steps.map((step: any) => ({
            id: step.id,
            name: step.name,
            type: step.type,
            status: step.status,
            startedAt: step.startedAt,
            completedAt: step.completedAt,
            consultationConfig: prev.steps.find(s => s.id === step.id)?.consultationConfig
          }))
        } : null);

        // Log progress updates
        if (execution.status === 'completed') {
          addLog('success', 'Workflow execution completed!');
          onNotification('Workflow execution completed successfully');
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
          setIsExecuting(false);
        } else if (execution.status === 'failed') {
          addLog('error', `Workflow execution failed: ${execution.error || 'Unknown error'}`);
          onNotification('Workflow execution failed');
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
          setIsExecuting(false);
        }
      }
    } catch (error) {
      console.error('Failed to update execution status:', error);
      addLog('warning', `Failed to update status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const executeWorkflow = async (executionId: string, status: ExecutionStatus) => {
    try {
      // Start polling for execution updates
      intervalRef.current = setInterval(async () => {
        await updateExecutionStatus(executionId);
      }, 2000);

      // Simulate workflow execution
      await simulateWorkflowExecution(status);

    } catch (error) {
      console.error('Workflow execution failed:', error);
      addLog('error', 'Workflow execution failed');
      onNotification('Workflow execution failed');
    }
  };

  const simulateWorkflowExecution = async (status: ExecutionStatus) => {
    const steps = [...status.steps];

    for (let i = 0; i < steps.length; i++) {
      const step = steps[i];

      // Update current step
      setExecutionStatus(prev => prev ? {
        ...prev,
        currentStepIndex: i,
        steps: prev.steps.map((s, index) =>
          index === i ? { ...s, status: 'running', startedAt: new Date().toISOString() } : s
        )
      } : null);

      addLog('info', `Starting step: ${step.name}`, step.id);

      // Simulate step execution time
      const executionTime = getStepExecutionTime(step.type);
      await new Promise(resolve => setTimeout(resolve, executionTime));

      // Simulate agent consultation if enabled
      if (step.consultationConfig?.enabled && enableAgentConsultation) {
        await simulateAgentConsultation(step);
      }

      // Complete step
      const completedAt = new Date().toISOString();
      const duration = Date.now() - new Date(step.startedAt || Date.now()).getTime();

      setExecutionStatus(prev => prev ? {
        ...prev,
        steps: prev.steps.map((s, index) =>
          index === i ? {
            ...s,
            status: 'completed',
            completedAt,
            duration,
            output: generateStepOutput(step.type)
          } : s
        )
      } : null);

      addLog('success', `Completed step: ${step.name} (${duration}ms)`, step.id);
    }

    // Complete workflow
    const completedAt = new Date().toISOString();
    const totalDuration = Date.now() - new Date(status.startedAt).getTime();

    setExecutionStatus(prev => prev ? {
      ...prev,
      status: 'completed',
      completedAt,
      totalDuration
    } : null);

    setIsExecuting(false);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    addLog('success', `Workflow completed successfully (${totalDuration}ms)`);
    onNotification('Workflow execution completed successfully');
  };

  const simulateAgentConsultation = async (step: WorkflowStep) => {
    const agents = step.consultationConfig?.triggers[0]?.agents || [];
    const consultationResults = [];

    for (const agentId of agents) {
      addLog('info', `Consulting ${agentId} agent`, step.id, agentId);

      // Simulate consultation time
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

      const confidence = 0.7 + Math.random() * 0.3; // 0.7 to 1.0
      const processingTime = 1000 + Math.random() * 2000;

      consultationResults.push({
        agentId,
        confidence,
        suggestions: generateAgentSuggestions(agentId),
        processingTime
      });

      // Update consultation counts
      setExecutionStatus(prev => prev ? {
        ...prev,
        agentConsultations: prev.agentConsultations + 1,
        successfulConsultations: prev.successfulConsultations + 1
      } : null);

      addLog('success', `${agentId} consultation completed (${Math.round(confidence * 100)}% confidence)`, step.id, agentId);
    }

    // Update step with consultation results
    setExecutionStatus(prev => prev ? {
      ...prev,
      steps: prev.steps.map(s =>
        s.id === step.id ? { ...s, consultationResults } : s
      )
    } : null);
  };

  const updateExecutionStatus = async (executionId: string) => {
    try {
      // In a real implementation, this would fetch from the backend
      // For now, we'll just update the UI based on the current simulation
    } catch (error) {
      console.error('Failed to update execution status:', error);
    }
  };

  const getStepExecutionTime = (stepType: string): number => {
    const baseTimes = {
      'TEXT_INPUT': 500,
      'AI_GENERATION': 3000,
      'HUMAN_REVIEW': 2000,
      'CSV_IMPORT': 1000,
      'CSV_EXPORT': 800,
      'URL_FETCH': 1500,
      'LOOP': 4000
    };

    return baseTimes[stepType as keyof typeof baseTimes] || 2000;
  };

  const generateStepOutput = (stepType: string): any => {
    switch (stepType) {
      case 'AI_GENERATION':
        return {
          content: 'Generated content based on inputs and agent consultation',
          wordCount: 1500,
          confidence: 0.85
        };
      case 'CSV_IMPORT':
        return {
          rowsImported: 150,
          columnsDetected: 5
        };
      case 'URL_FETCH':
        return {
          contentLength: 2500,
          title: 'Fetched Content Title'
        };
      default:
        return {
          status: 'completed',
          message: 'Step completed successfully'
        };
    }
  };

  const generateAgentSuggestions = (agentId: string): any[] => {
    const suggestions = {
      'seo-keyword': [
        { area: 'Keywords', suggestion: 'Include long-tail keywords for better ranking', priority: 'high' },
        { area: 'Meta Description', suggestion: 'Optimize meta description length', priority: 'medium' }
      ],
      'market-research': [
        { area: 'Audience', suggestion: 'Target emerging market segments', priority: 'high' },
        { area: 'Competitors', suggestion: 'Analyze competitor content gaps', priority: 'medium' }
      ],
      'content-strategy': [
        { area: 'Structure', suggestion: 'Improve content flow and readability', priority: 'high' },
        { area: 'CTA', suggestion: 'Add stronger call-to-action elements', priority: 'medium' }
      ]
    };

    return suggestions[agentId as keyof typeof suggestions] || [];
  };

  const addLog = (level: 'info' | 'warning' | 'error' | 'success', message: string, stepId?: string, agentId?: string) => {
    setExecutionLogs(prev => [{
      timestamp: new Date().toISOString(),
      level,
      message,
      stepId,
      agentId
    }, ...prev.slice(0, 49)]); // Keep last 50 logs
  };

  const getStepIcon = (type: string) => {
    switch (type) {
      case 'TEXT_INPUT': return '📝';
      case 'AI_GENERATION': return '🤖';
      case 'HUMAN_REVIEW': return '👤';
      case 'CSV_IMPORT': return '📊';
      case 'CSV_EXPORT': return '💾';
      case 'URL_FETCH': return '🌐';
      case 'LOOP': return '🔄';
      default: return '⚙️';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-gray-100 text-gray-800';
      case 'running': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'skipped': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getLogLevelColor = (level: string) => {
    switch (level) {
      case 'info': return 'text-blue-600';
      case 'warning': return 'text-yellow-600';
      case 'error': return 'text-red-600';
      case 'success': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  const getLogLevelIcon = (level: string) => {
    switch (level) {
      case 'info': return 'ℹ️';
      case 'warning': return '⚠️';
      case 'error': return '❌';
      case 'success': return '✅';
      default: return '📝';
    }
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const handlePauseWorkflow = async () => {
    if (!executionStatus) return;

    try {
      const response = await fetch('/api/workflow/control', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'pause',
          executionId: executionStatus.executionId
        })
      });

      const result = await response.json();
      if (result.success) {
        setExecutionStatus(prev => prev ? { ...prev, status: 'paused' } : null);
        addLog('info', 'Workflow paused by user');
        onNotification('Workflow paused successfully');
      } else {
        onNotification(`Failed to pause workflow: ${result.error}`);
      }
    } catch (error) {
      console.error('Failed to pause workflow:', error);
      onNotification('Failed to pause workflow');
    }
  };

  const handleResumeWorkflow = async () => {
    if (!executionStatus) return;

    try {
      const response = await fetch('/api/workflow/control', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'resume',
          executionId: executionStatus.executionId
        })
      });

      const result = await response.json();
      if (result.success) {
        setExecutionStatus(prev => prev ? { ...prev, status: 'running' } : null);
        addLog('info', 'Workflow resumed by user');
        onNotification('Workflow resumed successfully');
      } else {
        onNotification(`Failed to resume workflow: ${result.error}`);
      }
    } catch (error) {
      console.error('Failed to resume workflow:', error);
      onNotification('Failed to resume workflow');
    }
  };

  const handleStopWorkflow = async () => {
    if (!executionStatus) return;

    if (!confirm('Are you sure you want to stop this workflow? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch('/api/workflow/control', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'stop',
          executionId: executionStatus.executionId
        })
      });

      const result = await response.json();
      if (result.success) {
        setExecutionStatus(prev => prev ? { ...prev, status: 'failed' } : null);
        setIsExecuting(false);
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
        addLog('warning', 'Workflow stopped by user');
        onNotification('Workflow stopped');
      } else {
        onNotification(`Failed to stop workflow: ${result.error}`);
      }
    } catch (error) {
      console.error('Failed to stop workflow:', error);
      onNotification('Failed to stop workflow');
    }
  };

  if (!workflow) {
    return (
      <div className="p-6 text-center">
        <div className="text-gray-400 mb-4">
          <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">No Workflow Selected</h3>
        <p className="text-gray-600">
          Create a workflow first to execute it.
        </p>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Execute Workflow</h2>
        <p className="text-gray-600">
          Configure inputs and run your workflow with intelligent agent consultation
        </p>
      </div>

      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Input Validation Errors</h3>
              <div className="mt-2 text-sm text-red-700">
                <ul className="list-disc pl-5 space-y-1">
                  {validationErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Workflow Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">⚡</span>
          <div className="flex-1">
            <h3 className="font-medium text-gray-900">{workflow.name}</h3>
            <p className="text-sm text-gray-600">{workflow.description}</p>
            <div className="flex items-center space-x-4 mt-2">
              <span className="text-sm text-gray-500">
                {workflow.steps?.length || 0} steps
              </span>
              {workflow.agentConsultationEnabled && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  🤖 Agent Consultation Enabled
                </span>
              )}
              {workflow.metadata?.estimatedTime && (
                <span className="text-sm text-gray-500">
                  ~{workflow.metadata.estimatedTime} min
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Workflow Inputs */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Workflow Inputs</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Topic *
            </label>
            <input
              type="text"
              value={inputs.topic || ''}
              onChange={(e) => setInputs(prev => ({ ...prev, topic: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter the main topic..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Target Audience *
            </label>
            <input
              type="text"
              value={inputs.target_audience || ''}
              onChange={(e) => setInputs(prev => ({ ...prev, target_audience: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              placeholder="Describe your target audience..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Primary Keyword
            </label>
            <input
              type="text"
              value={inputs.primary_keyword || ''}
              onChange={(e) => setInputs(prev => ({ ...prev, primary_keyword: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter primary keyword..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Content Goals
            </label>
            <input
              type="text"
              value={inputs.goals || ''}
              onChange={(e) => setInputs(prev => ({ ...prev, goals: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              placeholder="What do you want to achieve?"
            />
          </div>
        </div>

        <div className="mt-6 flex items-center justify-between">
          <div className="text-sm text-gray-600">
            {validationErrors.length === 0 ? (
              <span className="text-green-600">✅ All inputs are valid</span>
            ) : (
              <span className="text-red-600">❌ Please fix validation errors above</span>
            )}
          </div>

          <button
            onClick={handleStartExecution}
            disabled={validationErrors.length > 0 || isExecuting}
            className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isExecuting ? 'Executing...' : 'Start Execution'}
          </button>
        </div>
      </div>

      {/* Execution Status */}
      {executionStatus && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              Execution Status: {executionStatus.executionId.slice(-8)}
            </h3>
            <div className="flex items-center space-x-4">
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(executionStatus.status)}`}>
                {executionStatus.status}
              </span>
              {executionStatus.status === 'running' && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span>Step {executionStatus.currentStepIndex + 1} of {executionStatus.steps.length}</span>
                </div>
              )}

              {/* Workflow Controls */}
              <div className="flex items-center space-x-2">
                {executionStatus.status === 'running' && (
                  <button
                    onClick={handlePauseWorkflow}
                    className="px-3 py-1 text-sm bg-yellow-600 text-white rounded hover:bg-yellow-700 transition-colors"
                  >
                    ⏸️ Pause
                  </button>
                )}
                {executionStatus.status === 'paused' && (
                  <button
                    onClick={handleResumeWorkflow}
                    className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
                  >
                    ▶️ Resume
                  </button>
                )}
                {(executionStatus.status === 'running' || executionStatus.status === 'paused') && (
                  <button
                    onClick={handleStopWorkflow}
                    className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                  >
                    ⏹️ Stop
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Execution Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{executionStatus.steps.length}</div>
              <div className="text-sm text-gray-600">Total Steps</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {executionStatus.steps.filter(s => s.status === 'completed').length}
              </div>
              <div className="text-sm text-gray-600">Completed</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{executionStatus.agentConsultations}</div>
              <div className="text-sm text-gray-600">Agent Consultations</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {executionStatus.totalDuration ? formatDuration(executionStatus.totalDuration) : 'Running...'}
              </div>
              <div className="text-sm text-gray-600">Duration</div>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mb-6">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Progress</span>
              <span>
                {Math.round((executionStatus.steps.filter(s => s.status === 'completed').length / executionStatus.steps.length) * 100)}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{
                  width: `${(executionStatus.steps.filter(s => s.status === 'completed').length / executionStatus.steps.length) * 100}%`
                }}
              ></div>
            </div>
          </div>

          {/* Steps List */}
          <div className="space-y-3">
            {executionStatus.steps.map((step, index) => (
              <div key={step.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-3">
                    <span className="text-xl">{getStepIcon(step.type)}</span>
                    <div>
                      <h4 className="font-medium text-gray-900">{step.name}</h4>
                      <p className="text-sm text-gray-500">Type: {step.type}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    {step.consultationConfig?.enabled && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        🤖 Agent Consultation
                      </span>
                    )}
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(step.status)}`}>
                      {step.status}
                    </span>
                  </div>
                </div>

                {step.duration && (
                  <div className="text-sm text-gray-600 mb-2">
                    Duration: {formatDuration(step.duration)}
                  </div>
                )}

                {/* Agent Consultation Results */}
                {step.consultationResults && step.consultationResults.length > 0 && (
                  <div className="mt-3 pt-3 border-t border-gray-100">
                    <h5 className="text-sm font-medium text-gray-900 mb-2">
                      Agent Consultation Results ({step.consultationResults.length})
                    </h5>
                    <div className="space-y-2">
                      {step.consultationResults.map((result, resultIndex) => (
                        <div key={resultIndex} className="bg-gray-50 rounded-lg p-3">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-gray-900">{result.agentId}</span>
                            <div className="text-sm text-gray-600">
                              {Math.round(result.confidence * 100)}% confidence | {formatDuration(result.processingTime)}
                            </div>
                          </div>
                          {result.suggestions.length > 0 && (
                            <div className="text-sm text-gray-600">
                              {result.suggestions.length} suggestions provided
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {step.error && (
                  <div className="mt-3 pt-3 border-t border-gray-100">
                    <div className="text-sm text-red-600">
                      Error: {step.error}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Execution Logs */}
      {executionLogs.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              Execution Logs ({executionLogs.length})
            </h3>
            <button
              onClick={() => setExecutionLogs([])}
              className="text-sm text-red-600 hover:text-red-800"
            >
              Clear Logs
            </button>
          </div>

          <div className="space-y-2 max-h-64 overflow-y-auto">
            {executionLogs.map((log, index) => (
              <div key={index} className="flex items-start space-x-3 text-sm">
                <span className="text-lg">{getLogLevelIcon(log.level)}</span>
                <div className="flex-1">
                  <div className={`font-medium ${getLogLevelColor(log.level)}`}>
                    {log.message}
                  </div>
                  <div className="text-gray-500 text-xs">
                    {formatTimestamp(log.timestamp)}
                    {log.stepId && ` | Step: ${log.stepId}`}
                    {log.agentId && ` | Agent: ${log.agentId}`}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
